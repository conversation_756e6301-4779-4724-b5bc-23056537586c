/* sidepanel.css */
/* Common styles for sidepanel - Expanded */
html {
  display: flex;
  min-height: 100%;
}
body {
  display: flex; /* Use flexbox for layout */
  flex-direction: column; /* Stack elements vertically */
  height: 100vh; /* Full viewport height */
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif; /* System fonts */
  padding: 0; /* Remove body padding, handle padding in containers */
  margin: 0;
  background-color: #f8f9fa; /* Light background */
  color: #212529; /* Default text color */
  font-size: 14px; /* Base font size */
  overflow: hidden; /* Prevent body scroll */
}

/* Style adjustments for jQuery UI elements */
.ui-widget {
    font-family: inherit; /* Use body font */
    font-size: inherit;
}

.ui-widget-content {
    border-color: #dee2e6; /* Lighter border */
    background-color: #fff;
    color: #212529;
}

.ui-widget-header {
    border-color: #dee2e6;
    background: #e9ecef; /* Lighter header */
    color: #495057;
    font-weight: bold;
}

.ui-state-default,
.ui-widget-content .ui-state-default,
.ui-widget-header .ui-state-default {
    border-color: #ced4da;
    background: #f8f9fa;
    font-weight: normal;
    color: #495057;
}

.ui-state-hover,
.ui-widget-content .ui-state-hover,
.ui-widget-header .ui-state-hover,
.ui-state-focus,
.ui-widget-content .ui-state-focus,
.ui-widget-header .ui-state-focus {
    border-color: #adb5bd;
    background: #e9ecef;
    font-weight: normal;
    color: #212529;
}

.ui-state-active,
.ui-widget-content .ui-state-active,
.ui-widget-header .ui-state-active {
    border-color: #007bff;
    background: #007bff;
    font-weight: bold;
    color: #ffffff;
}

/* --- Common Form Element Styling --- */
.field-group {
  margin-bottom: 12px;
}
.field-group label {
  display: block;
  font-weight: bold;
  margin-bottom: 4px;
  font-size: 12px;
  color: #333;
}
.field-group input[type="text"],
.field-group input[type="number"],
.field-group input[type="url"],
.field-group input[type="email"],
.field-group textarea,
.field-group select {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid #ccc;
  border-radius: 3px;
  font-size: 12px;
  box-sizing: border-box; /* Include padding and border in width */
  background-color: #fff;
  color: #333;
}
.field-group textarea {
  font-family: monospace;
  min-height: 50px;
  resize: vertical;
}
.field-group select {
  height: 30px; /* Consistent height */
  appearance: none; /* Remove default arrow */
  background-image: url('data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%23007bff%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.5-12.8z%22%2F%3E%3C%2Fsvg%3E');
  background-repeat: no-repeat;
  background-position: right .7em top 50%;
  background-size: .65em auto;
  padding-right: 2em; /* Space for custom arrow */
}
.field-group input:read-only {
  background-color: #e9ecef;
  cursor: not-allowed;
}
.field-group input:disabled,
.field-group select:disabled,
.field-group textarea:disabled {
    background-color: #e9ecef;
    cursor: not-allowed;
    opacity: 0.7;
}

/* Styling for checkbox fields */
.checkbox-field {
  display: flex;
  align-items: center;
  margin-bottom: 5px; /* Adjust as needed */
}
.checkbox-field input[type="checkbox"] {
  margin-right: 8px;
  width: auto; /* Override full width */
  height: auto;
  flex-shrink: 0;
}
.checkbox-field label {
  margin-bottom: 0; /* Override default label margin */
  font-weight: normal;
  font-size: 12px;
}

/* Styling for input groups with a button inside */
.field-group-with-button {
  position: relative;
}
.field-group-with-button input {
  padding-right: 30px !important; /* Make space for the button */
}
.field-group .field-button {
    position: absolute;
    right: 4px;
    top: 23px;
    margin-top: 0;
    padding: 0;
    height: 22px;
    width: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    color: #007bff;
    background: none;
    border: 1px solid #007bff;
    border-radius: 3px;
    line-height: 1;
    font-weight: normal;
    cursor: pointer;
    z-index: 2;
    transition: background-color 0.2s ease, color 0.2s ease;
}
.field-group .field-group-with-button .field-button {
    top: 50%;
    transform: translateY(-50%);
}
.field-group .field-button svg {
  display: block;
  fill: #007bff;
  width: 10px;
  height: 10px;
  transition: fill 0.2s ease;
}
.field-group .field-button:hover {
  background-color: #007bff;
  color: #fff;
}
.field-group .field-button:hover svg {
  fill: #fff;
}
.field-group .field-button:disabled {
  background: #eee;
  border-color: #ccc;
  cursor: not-allowed;
  color: #aaa;
}
.field-group .field-button:disabled svg {
  fill: #ccc;
}

/* Screenshots container styling */
.screenshots-container {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  flex-wrap: wrap;
}

.screenshot-capture-button {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #007bff;
  color: white;
  border: 1px solid #007bff;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  flex-shrink: 0;
}

.screenshot-capture-button svg {
  width: 20px;
  height: 20px;
  fill: white;
}

.screenshot-capture-button:hover:not(:disabled) {
  background-color: #0056b3;
  border-color: #0056b3;
}

.screenshot-capture-button:disabled {
  background-color: #6c757d;
  border-color: #6c757d;
  cursor: not-allowed;
}

.screenshots-display {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  flex: 1;
}

.screenshot-thumbnail {
  position: relative;
  width: 40px;
  height: 40px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  background-color: #f8f9fa;
  cursor: pointer;
}

.screenshot-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.screenshot-thumbnail .remove-screenshot {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 16px;
  height: 16px;
  padding: 0;
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

.screenshot-thumbnail .remove-screenshot:hover {
  opacity: 1;
}

.screenshot-thumbnail .remove-screenshot::before {
  content: 'x';
  font-size: 12px;
  line-height: 1;
}


/* --- Common Button Styles --- */
button {
    cursor: pointer;
    padding: 6px 12px;
    font-size: 0.9em;
    border-radius: 4px;
    border: 1px solid transparent;
    transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease;
    line-height: 1.5; /* Ensure text aligns well */
}

button:disabled {
    cursor: not-allowed;
    opacity: 0.65;
}

button.primary {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
}
button.primary:hover:not(:disabled) {
  background-color: #0056b3;
  border-color: #0056b3;
}

button.secondary {
  background-color: #6c757d;
  color: white;
  border-color: #6c757d;
}
button.secondary:hover:not(:disabled) {
  background-color: #5a6268;
  border-color: #5a6268;
}

button.danger {
  background-color: #dc3545;
  color: white;
  border-color: #dc3545;
}
button.danger:hover:not(:disabled) {
  background-color: #c82333;
  border-color: #c82333;
}

button.small-button {
  padding: 4px 8px;
  font-size: 11px;
  line-height: 1.2;
}

/* --- Accordion Styling --- */

/* Common container for accordion items (fields or options) */
.dynamic-field-container,
.option-item {
    border: 1px solid #dee2e6;
    border-radius: 4px;
    margin-bottom: 8px; /* Add gap between items */
    background-color: #fff; /* Ensure background */
    overflow: hidden; /* Prevent content bleed */
    transition: border-color 0.2s ease, background-color 0.2s ease; /* Smooth transition for border color changes */
}

/* Common Header Styles (Fields H3, Options H4) */
.dynamic-field-container > h3.ui-accordion-header,
.option-item > h4.ui-accordion-header {
    background-color: #f8f9fa; /* Default background */
    border: none !important; /* Remove default accordion border */
    border-bottom: 1px solid #dee2e6 !important; /* Separator line */
    color: #495057;
    padding: 8px 12px; /* Adjust padding */
    margin: 0; /* Remove default margins */
    font-size: 13px;
    font-weight: bold;
    cursor: pointer; /* Indicate clickable */
    position: relative; /* For absolute positioned buttons inside */
    border-radius: 0; /* Remove individual radius */
    transition: border-color 0.2s ease, background-color 0.2s ease; /* Smooth transition */
}
/* Specific header font size for options */
.option-item > h4.ui-accordion-header {
    font-size: 12px;
}

/* Header styles when active/open */
.dynamic-field-container > h3.ui-accordion-header.ui-state-active,
.option-item > h4.ui-accordion-header.ui-state-active {
    background-color: #007bff; /* Active background */
    color: #fff;
    border-bottom-color: #007bff; /* Match background */
}


/* Header styles on hover */
.dynamic-field-container > h3.ui-accordion-header.ui-state-hover,
.option-item > h4.ui-accordion-header.ui-state-hover {
    background-color: #e9ecef; /* Hover background */
    border-bottom-color: #dee2e6;
    color: #212529;
}
/* Active + Hover */
.dynamic-field-container > h3.ui-accordion-header.ui-state-active.ui-state-hover,
.option-item > h4.ui-accordion-header.ui-state-active.ui-state-hover {
    background-color: #0056b3; /* Darker blue on hover when active */
    border-bottom-color: #0056b3;
    color: #fff;
}

/* Apply top radius only to the first header within its container */
#admin-form-fields-content > .dynamic-field-container:first-child > h3.ui-accordion-header,
.options-container > .option-item:first-child > h4.ui-accordion-header {
   border-top-left-radius: 3px;
   border-top-right-radius: 3px;
}
/* Apply bottom radius to the last header *if it's not active* */
#admin-form-fields-content > .dynamic-field-container:last-child > h3.ui-accordion-header:not(.ui-state-active),
.options-container > .option-item:last-child > h4.ui-accordion-header:not(.ui-state-active) {
   border-bottom-left-radius: 3px;
   border-bottom-right-radius: 3px;
   border-bottom: none; /* Remove bottom border if it's the last and closed */
}


/* Common Panel Styles (Fields .field-panel, Options .option-panel) */
.dynamic-field-container > .ui-accordion-content,
.option-item > .ui-accordion-content {
    background-color: #fff;
    border: none; /* Remove default accordion border */
    border-top: none; /* Content doesn't need top border */
    padding: 15px; /* Content padding */
    overflow: visible; /* Allow content to flow */
    border-radius: 0; /* Remove individual radius */
}
/* Specific padding for option panels */
.option-item > .ui-accordion-content {
    padding: 10px;
}

/* Apply bottom radius only to the last panel *when it's active* */
#admin-form-fields-content > .dynamic-field-container:last-child > .ui-accordion-content.ui-accordion-content-active,
.options-container > .option-item:last-child > .ui-accordion-content.ui-accordion-content-active {
   border-bottom-left-radius: 3px;
   border-bottom-right-radius: 3px;
}


/* --- Specific Component Styling --- */

/* Placeholder text styling */
.no-report {
    color: #6c757d;
    font-style: italic;
    padding: 20px;
    font-size: 0.9em;
    text-align: center;
    background-color: #f8f9fa;
    border: 1px dashed #dee2e6;
    border-radius: 4px;
}

/* Options Section within Admin Fields */
.options-section {
  margin-top: 15px;
  border-top: 1px dashed #ccc;
  padding-top: 15px;
}
.options-section h4 { /* The static "Options (...)" header */
  font-size: 12px;
  margin: 0 0 10px 0;
  color: #555;
  font-weight: bold;
}
.remove-field-button,
.remove-option-button {
  position: absolute;
  top: 50%;
  right: 8px;
  transform: translateY(-50%);
  z-index: 5;
  background: none;
  border: none;
  color: #dc3545; /* Default color */
  cursor: pointer;
  padding: 2px 4px;
  opacity: 0.7;
  line-height: 1;
}
.remove-field-button svg,
.remove-option-button svg {
  width: 12px;
  height: 12px;
  fill: currentColor;
  vertical-align: middle; /* Align icon better */
}
.remove-field-button:hover,
.remove-option-button:hover {
  opacity: 1;
  color: #a0101d; /* Darker red on hover */
}
/* Adjust color when header is active */
.ui-state-active .remove-field-button,
.ui-state-active .remove-option-button {
    color: #fff; /* White on active header */
    opacity: 0.8;
}
.ui-state-active .remove-field-button:hover,
.ui-state-active .remove-option-button:hover {
    opacity: 1;
}


/* Submit Tab Properties Table */
#submit-tab .properties-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 10px;
  font-size: 11px;
}
#submit-tab .properties-table th,
#submit-tab .properties-table td {
  padding: 5px;
  text-align: left;
  border-bottom: 1px solid #eee;
  vertical-align: top;
}
#submit-tab .properties-table th {
  font-weight: bold;
  width: 35%;
  color: #555;
}
#submit-tab .properties-table td {
  word-break: break-all;
  color: #333;
  font-family: monospace;
}
#submit-tab .properties-table td code {
  background-color: #e9ecef;
  padding: 1px 3px;
  border-radius: 2px;
  font-size: 10px;
  display: inline-block; /* Prevent breaking inside code */
}

#submit-tab .properties-table td .selector-input {
  width: 100%;
  font-family: monospace;
  font-size: 10px;
  padding: 2px 4px;
  background-color: #e9ecef;
  border: 1px solid #ced4da;
  border-radius: 2px;
  margin: 0;
  /* Ensure no validation styling is applied */
  box-shadow: none !important;
}
#submit-tab .properties-table tr:last-child th,
#submit-tab .properties-table tr:last-child td {
  border-bottom: none;
}

/* Submit Tab Value Container */
#submit-tab .value-container {
  margin-top: 8px;
}
#submit-tab .value-container label {
  margin-bottom: 3px;
  font-size: 12px;
  font-weight: bold;
}
#submit-tab .value-container textarea {
  min-height: 40px;
  background-color: #f0f8ff; /* Light blue background */
  border-color: #cce5ff;
  font-size: 18px;
  width: 100%;
  margin-top: 5px;
}